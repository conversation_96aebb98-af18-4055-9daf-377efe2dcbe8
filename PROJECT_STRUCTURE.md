# 项目结构说明

## 概述
本项目是一个基于ToxiGenRoBERTa的多智能体仇恨言论检测系统，已完成模块化重构。

## 目录结构

```
paper_test2/
├── 📁 core_modules/                    # 核心模块（新增）
│   ├── __init__.py
│   ├── 📁 agents/                      # 智能体模块
│   │   ├── __init__.py
│   │   ├── toxigen_roberta_agent.py    # ToxiGenRoBERTa智能体（主要模型）
│   │   └── enhanced_deberta_agent.py   # 增强DeBERTa智能体（辅助）
│   └── 📁 systems/                     # 系统模块
│       ├── __init__.py
│       └── async_multiagent_system.py  # 异步多智能体系统
│
├── 📁 offensive_speech_detection/      # 原有检测模块
│   ├── __init__.py
│   ├── models.py                       # 已更新为使用ToxiGenRoBERTa
│   ├── evaluator.py
│   └── data_loader.py
│
├── 📁 datasets/                        # 数据集目录（仅支持英文数据集）
│   ├── implicit-hate-corpus/           # ImplicitHate数据集
│   ├── hate-speech-and-offensive-language/  # HateSpeechOffensive数据集
│   └── hate-speech-dataset/            # HateSpeechStormfront数据集
│
├── 📁 logs/                           # 评估日志
├── 📁 results/                        # 评估结果和图表
├── 📁 vector_db/                      # 向量数据库
├── 📁 word_dic/                       # 词典资源
│
├── 🚀 run_comparison_evaluation.py     # 对比评估脚本
├── 🚀 run_multi_agent_evaluation.py   # 多智能体评估脚本
├── 🚀 run_single_agent_evaluation.py  # 单智能体评估脚本
│
└── 📄 PROJECT_STRUCTURE.md            # 本文档
```

## 核心变更

### 1. 模型替换
- ✅ **TwitterRoBERTa** → **ToxiGenRoBERTa**
- ✅ 更新了所有相关引用和配置
- ✅ 保持了API兼容性

### 2. 模块化重构
- ✅ 创建了 `core_modules/` 目录
- ✅ 智能体模块化到 `core_modules/agents/`
- ✅ 系统模块化到 `core_modules/systems/`
- ✅ 清理了临时测试文件

### 3. 清理工作
- ✅ 删除了冗余的深度学习模型代码
- ✅ 删除了临时测试文件
- ✅ 保留了三个主要评估脚本在根目录

## 使用方法

### 运行评估
```bash
# 多智能体评估（推荐）
python run_multi_agent_evaluation.py --dataset ImplicitHate --num_samples 1000 --start_idx 0 --provider api --model gpt-3.5-turbo-0125

# 对比评估
python run_comparison_evaluation.py --dataset ImplicitHate --num_samples 1000 --start_idx 0 --provider api --model gpt-3.5-turbo-0125

# 单智能体评估
python run_single_agent_evaluation.py --dataset ImplicitHate --num_samples 1000 --start_idx 0 --provider api --model gpt-3.5-turbo-0125
```

### 直接使用ToxiGenRoBERTa智能体
```python
from core_modules.agents import ToxiGenRoBERTaAgent

agent = ToxiGenRoBERTaAgent()
result = agent.detect("Your text here")
print(f"Verdict: {result['verdict']}, Reasoning: {result['reasoning']}")
```

## 模型信息

### ToxiGenRoBERTa
- **路径**: `D:/models/toxigen_roberta`
- **类型**: 基于RoBERTa的仇恨言论检测模型
- **特点**: 专门针对隐式和显式仇恨言论检测，使用对抗性生成训练
- **标签**: LABEL_0 (正常), LABEL_1 (仇恨言论)
- **性能**: 在评估中表现最佳，特别是在隐式仇恨言论检测上

## 测试状态
- ✅ ToxiGenRoBERTa智能体测试通过
- ✅ 多智能体系统测试通过（10条数据）
- ✅ 评估脚本正常运行
- ✅ 模块化导入正常工作

## 注意事项
1. 确保模型路径 `D:/models/toxigen_roberta` 存在
2. 需要CUDA环境以获得最佳性能
3. API调用需要有效的API密钥配置
4. 向量数据库会在首次运行时自动创建
