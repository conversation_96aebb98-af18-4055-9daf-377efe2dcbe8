import argparse
import os
import time
import json
from datetime import datetime
from offensive_speech_detection.models import (
    MultiAgentOrchestrator, VectorDatabaseManager,
    create_advanced_system, AdvancedSystemConfig
)
from offensive_speech_detection.evaluator import ModelEvaluator
from offensive_speech_detection.data_loader import get_dataset_loader

def main():
    """Run multi-agent evaluation"""
    parser = argparse.ArgumentParser(description="Run multi-agent offensive speech detection evaluation")
    parser.add_argument("--dataset", type=str, default="DynamicallyHate",
                        choices=["CHSD", "COLDataset", "ToxiCN", "ImplicitHate",
                                "HateSpeechOffensive", "HateSpeechStormfront", "DynamicallyHate"],
                        help="Name of the dataset to evaluate")
    parser.add_argument("--num_samples", type=int, default=10,
                        help="Number of samples to evaluate")
    parser.add_argument("--start_idx", type=int, default=0,
                        help="Starting index of samples")
    parser.add_argument("--model", type=str, default="gpt-3.5-turbo-0125",
                        help="Name of the model to use (only required for api/ollama providers)")
    parser.add_argument("--provider", type=str, choices=["api", "ollama", "local"], default="api",
                        help="Model provider")
    parser.add_argument("--ollama-base-url", type=str, default="http://localhost:11434",
                        help="Ollama API base URL")
    parser.add_argument("--local-model-path", type=str,
                        help="Path to local model (used when provider is 'local')")
    parser.add_argument("--vector-db-path", type=str, default="./vector_db",
                        help="Path to store vector database")
    parser.add_argument("--skip-vectordb-creation", action="store_true",
                        help="Skip vector database creation if it already exists")
    parser.add_argument("--n-results", type=int, default=3,
                        help="Number of similar examples to retrieve")

    # 新增高级系统参数 (默认使用智能协调系统)
    parser.add_argument("--system-type", type=str, default="intelligent_coordination",
                        choices=["multiagent_orchestrator", "intelligent_coordination"],
                        help="Type of multi-agent system to use (默认: intelligent_coordination)")
    parser.add_argument("--enable-dynamic-weights", action="store_true",
                        help="Enable dynamic weight allocation")
    parser.add_argument("--enable-performance-tracking", action="store_true", default=True,
                        help="Enable performance tracking")
    parser.add_argument("--enable-online-learning", action="store_true",
                        help="Enable online learning")
    parser.add_argument("--coordination-method", type=str, default="dynamic_weighted",
                        choices=["static_weighted", "dynamic_weighted", "consensus_based"],
                        help="Coordination method for intelligent systems")
    parser.add_argument("--learning-rate", type=float, default=0.001,
                        help="Learning rate for online learning")
    parser.add_argument("--confidence-threshold", type=float, default=0.6,
                        help="Confidence threshold for decision making")
    parser.add_argument("--adaptation-threshold", type=float, default=0.1,
                        help="Adaptation threshold for dynamic learning")
    parser.add_argument("--weight-update-frequency", type=int, default=10,
                        help="Frequency of weight updates (every N predictions)")
    parser.add_argument("--enable-caching", action="store_true", default=True,
                        help="Enable result caching for improved performance")
    parser.add_argument("--cache-size", type=int, default=1000,
                        help="Maximum cache size for storing results")
    parser.add_argument("--enable-detailed-logging", action="store_true", default=True,
                        help="Enable detailed logging of coordination decisions")

    # 新增专家系统和架构搜索参数
    parser.add_argument("--enable-moe-load-balancing", action="store_true", default=True,
                        help="Enable load balancing in Mixture of Experts")
    parser.add_argument("--expert-specialization-threshold", type=float, default=0.7,
                        help="Threshold for expert specialization in MoE")
    parser.add_argument("--nas-search-iterations", type=int, default=50,
                        help="Number of iterations for Neural Architecture Search")
    parser.add_argument("--nas-population-size", type=int, default=20,
                        help="Population size for NAS evolutionary algorithm")
    parser.add_argument("--architecture-mutation-rate", type=float, default=0.1,
                        help="Mutation rate for NAS architecture evolution")
    parser.add_argument("--hybrid-fusion-method", type=str, default="weighted_average",
                        choices=["weighted_average", "voting", "confidence_based"],
                        help="Fusion method for hybrid advanced system")
    parser.add_argument("--performance-window-size", type=int, default=100,
                        help="Window size for performance tracking")

    args = parser.parse_args()

    if args.provider == 'local' and not args.local_model_path:
        parser.error("--local-model-path is required when using a local provider.")

    # 当使用本地模型时，自动从路径解析模型名称
    if args.provider == "local":
        args.model = os.path.basename(args.local_model_path.rstrip('/\\'))

    print(f"Starting multi-agent evaluation on {args.dataset} dataset")
    print(f"Number of samples: {args.num_samples}, starting index: {args.start_idx}")
    print(f"Model provider: {args.provider}, model: {args.model}")
    print(f"System type: {args.system_type}")

    # Set model path for local provider
    model_path = args.model
    if args.provider == "local":
        model_path = args.local_model_path
        print(f"Using local model at: {model_path}")

    # 创建向量数据库管理器
    vector_db_manager = VectorDatabaseManager(base_path=args.vector_db_path)

    # 创建系统实例
    if args.system_type == "multiagent_orchestrator":
        # 使用原有的MultiAgentOrchestrator（向后兼容）
        orchestrator = MultiAgentOrchestrator(
            model=model_path,
            provider=args.provider,
            ollama_base_url=args.ollama_base_url,
            local_model_path=model_path,
            vector_db_manager=vector_db_manager,
            dataset_name=args.dataset
        )
    else:
        # 使用新的高级系统
        config = AdvancedSystemConfig(
            system_type=args.system_type,
            enable_dynamic_weights=args.enable_dynamic_weights,
            enable_performance_tracking=args.enable_performance_tracking,
            enable_online_learning=args.enable_online_learning,
            learning_rate=args.learning_rate,
            confidence_threshold=args.confidence_threshold,
            coordination_method=args.coordination_method,
            adaptation_threshold=args.adaptation_threshold,
            weight_update_frequency=args.weight_update_frequency,
            enable_caching=args.enable_caching,
            cache_size=args.cache_size,
            enable_detailed_logging=args.enable_detailed_logging,
            enable_moe_load_balancing=args.enable_moe_load_balancing,
            expert_specialization_threshold=args.expert_specialization_threshold,
            nas_search_iterations=args.nas_search_iterations,
            nas_population_size=args.nas_population_size,
            architecture_mutation_rate=args.architecture_mutation_rate,
            hybrid_fusion_method=args.hybrid_fusion_method,
            performance_window_size=args.performance_window_size
        )

        orchestrator = create_advanced_system(
            system_type=args.system_type,
            model=model_path,
            provider=args.provider,
            ollama_base_url=args.ollama_base_url,
            local_model_path=model_path,
            vector_db_manager=vector_db_manager,
            dataset_name=args.dataset,
            config=config
        )

        print(f"Advanced system configuration:")
        print(f"  - Dynamic weights: {args.enable_dynamic_weights}")
        print(f"  - Performance tracking: {args.enable_performance_tracking}")
        print(f"  - Online learning: {args.enable_online_learning}")
        print(f"  - Coordination method: {args.coordination_method}")
        print(f"  - Learning rate: {args.learning_rate}")
        print(f"  - Confidence threshold: {args.confidence_threshold}")
        print(f"  - Adaptation threshold: {args.adaptation_threshold}")
        print(f"  - Weight update frequency: {args.weight_update_frequency}")
        print(f"  - Caching enabled: {args.enable_caching}")
        print(f"  - Detailed logging: {args.enable_detailed_logging}")
    
    # 准备训练数据用于向量数据库构建
    data_loader = get_dataset_loader(args.dataset)
    
    # 加载训练集
    train_file_mapping = {
        "DynamicallyHate": "datasets/Dynamically-Generated-Hate-Speech-Dataset/dynamically_hate_train.csv",
        "HateSpeechOffensive": "datasets/hate-speech-and-offensive-language/hate_speech_offensive_train.csv",
        "HateSpeechStormfront": "datasets/hate-speech-dataset/hate_speech_stormfront_train.csv",
        "ImplicitHate": "datasets/implicit-hate-corpus/implicit_hate_train.csv",
        "CHSD": "datasets/CHSD/train.csv",
        "COLDataset": "datasets/COLDataset/train.csv",
        "ToxiCN": "datasets/ToxiCN/toxicn_train.csv"
    }
    
    # 加载训练数据，准备向量数据库
    if not args.skip_vectordb_creation or not vector_db_manager.dataset_has_vectordb(args.dataset):
        print(f"Preparing vector database for {args.dataset} dataset...")
        
        # 直接获取所有训练数据
        train_data = []
        try:
            # 将原数据加载器的训练文件路径替换为训练集文件路径
            # 保存原始测试文件路径
            original_test_file = data_loader.test_file
            
            # 获取训练文件路径，并替换
            if args.dataset in train_file_mapping:
                data_loader.test_file = train_file_mapping[args.dataset]
                train_data = data_loader.load_data()
                # 恢复原始测试文件路径
                data_loader.test_file = original_test_file
            else:
                print(f"Warning: No training file mapping for {args.dataset}")
        except Exception as e:
            print(f"Error loading training data: {str(e)}")
        
        # 创建向量数据库
        if train_data:
            print(f"Loaded {len(train_data)} training samples")
            # 根据系统类型准备向量数据库
            if args.system_type == "multiagent_orchestrator":
                # 原有系统使用retrieval_agent
                orchestrator.retrieval_agent.prepare_dataset_vectordb(args.dataset, train_data)
            else:
                # 高级系统可能有不同的向量数据库准备方式
                if hasattr(orchestrator, 'llm_retrieval_agent'):
                    orchestrator.llm_retrieval_agent.prepare_dataset_vectordb(args.dataset, train_data)
                elif hasattr(orchestrator, 'retrieval_agent'):
                    orchestrator.retrieval_agent.prepare_dataset_vectordb(args.dataset, train_data)
                else:
                    print("Warning: No retrieval agent found for vector database preparation")
        else:
            print("Failed to load training data, cannot create vector database")
            return
    else:
        print(f"Using existing vector database for {args.dataset} dataset")
    
    # 创建评估器
    class MultiAgentEvaluator(ModelEvaluator):
        def _create_cleaned_log_entry(self, result: dict, sample_id: int) -> dict:
            """创建清理后格式的日志条目"""

            # 提取智能体预测结果（简化版）
            agent_predictions = {}
            if 'agent_results' in result:
                for agent_name, agent_data in result['agent_results'].items():
                    agent_predictions[agent_name] = {
                        'verdict': agent_data.get('verdict'),
                        'processing_time': round(agent_data.get('processing_time', 0), 3)
                    }

            # 构建清理后的条目
            cleaned_entry = {
                'sample_id': sample_id,
                'text': result.get('text', ''),
                'true_label': result.get('true_label'),
                'final_verdict': result.get('verdict'),
                'final_confidence': round(result.get('confidence', 0), 3),
                'agent_predictions': agent_predictions,
                'consensus_type': result.get('agent_consensus', 'unknown'),
                'coordination_method': result.get('coordination_method', 'unknown'),
                'weights': result.get('learned_weights', result.get('agent_weights', {})),
                'processing_time': round(result.get('processing_time', 0), 3)
            }

            # 四舍五入权重值
            if isinstance(cleaned_entry['weights'], dict) and cleaned_entry['weights']:
                cleaned_entry['weights'] = {
                    k: round(v, 3) for k, v in cleaned_entry['weights'].items()
                }

            return cleaned_entry

        def evaluate(self):
            """重写evaluate方法以处理多智能体编排"""
            detector_type = self.detector.__class__.__name__
            print(f"Starting evaluation of {detector_type} on {self.dataset_name} dataset (Run ID: {self.run_id})")
            print(f"Loaded {len(self.test_data)} test samples")
            
            for idx, sample in enumerate(self.test_data):
                current_sample_num = self.start_idx + idx + 1
                print(f"\nProcessing sample {current_sample_num}/{self.start_idx + len(self.test_data)}...")
                text = sample["text"]
                true_label = sample["label"]
                
                print(f"Text: {text}")
                print(f"True label: {true_label}")
                
                start_time = time.time()
                
                # 根据系统类型调用检测方法
                if hasattr(self.detector, 'detect'):
                    if args.system_type != "multiagent_orchestrator":
                        # 高级系统支持true_label参数用于在线学习
                        result = self.detector.detect(text, self.dataset_name, true_label)
                    else:
                        # 原有系统
                        result = self.detector.detect(text, self.dataset_name)
                else:
                    result = {"verdict": -1, "confidence": 0.5, "reasoning": "Detection method not found"}

                processing_time = time.time() - start_time
                self.processing_times.append(processing_time)

                result["text"] = text
                result["true_label"] = true_label
                result["processing_time"] = processing_time
                result["dataset"] = self.dataset_name

                # 添加系统特定信息
                if hasattr(self.detector, 'get_decision_info'):
                    decision_info = self.detector.get_decision_info()
                    result.update(decision_info)

                self.all_results.append(result)
            
            # Evaluation completed
            print(f"Evaluation completed for {self.dataset_name}.")

            metrics = self._calculate_metrics()

            # 获取系统类型信息
            system_type = getattr(self.detector, 'system_type', args.system_type)
            detector_type = "multiagent" if args.system_type == "multiagent_orchestrator" else system_type

            # 创建清理后格式的结果
            cleaned_results = []
            for i, result in enumerate(self.all_results):
                cleaned_entry = self._create_cleaned_log_entry(result, i + 1)
                cleaned_results.append(cleaned_entry)

            # 构建清理后格式的日志数据
            log_data = {
                'metadata': {
                    'run_id': self.run_id,
                    'dataset': self.dataset_name,
                    'model': self.detector.model,
                    'system_type': system_type,
                    'num_samples': self.num_samples,
                    'created_at': datetime.now().isoformat(),
                    'system_config': {
                        "enable_dynamic_weights": args.enable_dynamic_weights,
                        "enable_performance_tracking": args.enable_performance_tracking,
                        "enable_online_learning": args.enable_online_learning,
                        "coordination_method": args.coordination_method,
                        "learning_rate": args.learning_rate,
                        "confidence_threshold": args.confidence_threshold
                    }
                },
                'metrics': metrics,
                'results': cleaned_results
            }
            
            # 清理模型名称以用于文件名
            model_name = self.detector.model
            if '/' in model_name or '\\' in model_name:
                # 提取路径中的最后一部分作为模型名称
                model_name = os.path.basename(model_name)

            model_name_cleaned = model_name.replace('/', '_').replace('\\', '_')
            # 移除Windows系统不允许的字符（: * ? " < > |）
            model_name_cleaned = model_name_cleaned.replace(':', '_').replace('*', '_').replace('?', '_').replace('"', '_').replace('<', '_').replace('>', '_').replace('|', '_')
            log_file = os.path.join(self.log_dir, f"multiagent_{model_name_cleaned}_{self.dataset_name}_log_{self.run_id}_{self.timestamp}.json")
            
            with open(log_file, "w", encoding="utf-8") as f:
                json.dump(log_data, f, ensure_ascii=False, indent=2)
            
            print(f"\nResults saved to: {log_file}")
            
            self._print_metrics(metrics)
            
            return metrics, self.all_results
    
    # 创建评估器
    evaluator = MultiAgentEvaluator(
        detector=orchestrator,
        dataset_name=args.dataset,
        num_samples=args.num_samples,
        start_idx=args.start_idx
    )
    
    # 运行评估
    evaluator.evaluate()
    
    # 绘制指标
    evaluator.plot_metrics()

if __name__ == "__main__":
    main() 