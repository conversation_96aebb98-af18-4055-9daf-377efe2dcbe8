# 多智能体仇恨言论检测框架技术文档

## 概述

本文档介绍了一个基于智能协调的多智能体仇恨言论检测框架。该框架集成了三个不同类型的智能体：RoBERTa本地模型智能体、LLM单智能体和LLM检索增强智能体，通过先进的在线学习算法实现动态权重分配和协调决策，支持异步并行处理和智能缓存机制。

## 1. 框架架构

### 1.1 整体架构

框架采用智能协调多智能体架构（IntelligentCoordinationSystem），包含三个核心智能体和一个高级协调引擎。每个智能体独立并行进行推理，协调引擎负责动态权重分配、在线学习和最终决策融合。

### 1.2 组件结构

```
┌─────────────────────────────────────────────────────────────────────┐
│                    智能协调多智能体框架                              │
├─────────────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────────────┐  │
│  │   RoBERTa   │  │ LLM单智能体  │  │    LLM检索增强智能体         │  │
│  │  本地模型   │  │  (多模型)   │  │   (ChromaDB + LLM)         │  │
│  │ 专业训练    │  │             │  │                            │  │
│  └─────────────┘  └─────────────┘  └─────────────────────────────┘  │
│           │              │                         │                │
│           └──────────────┼─────────────────────────┘                │
│                          │                                          │
│  ┌─────────────────────────────────────────────────────────────────┐ │
│  │                  智能协调引擎                                    │ │
│  │  • 随机权重初始化        • 异步并行处理                         │ │
│  │  • 在线学习算法          • 智能缓存系统                         │ │
│  │  • 动态权重分配          • 性能监控                             │ │
│  │  • 加权融合决策          • 容错机制                             │ │
│  └─────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────┘
```

## 2. 智能体组件

### 2.1 RoBERTa本地模型智能体

**实现方式:**
- 基于twitter-roberta-base-hate-latest专业模型
- 专门针对Twitter仇恨言论检测训练
- 本地部署，支持CUDA加速
- 快速推理，适合实时处理

**技术规格:**
- 模型路径: `C:/Users/<USER>/Downloads/twitter-roberta-base-hate-latest`
- 输入长度: 最大512 tokens
- 推理时间: 平均0.01-0.17秒
- 设备支持: 自动选择CUDA/CPU
- 输出格式: {"verdict": 0/1, "confidence": float}
- 标签映射: {0: 'NOT-HATE', 1: 'HATE'}

**缓存机制:**
- LRU缓存大小: 1000条记录
- 显著提升重复文本处理速度

### 2.2 LLM单智能体

**支持的模型:**
- **OpenAI系列**: GPT-3.5-turbo-0125, GPT-4o-2024-08-06, GPT-4-0613
- **Claude系列**: claude-3-5-sonnet-20240620, claude-3-7-sonnet-20250219
- **Gemini系列**: gemini-1.5-flash, gemini-1.5-pro
- **DeepSeek系列**: deepseek-v3, deepseek-chat
- **本地模型**: 支持ChatGLM、Qwen等本地部署模型

**实现特点:**
- 多API提供商支持（OpenAI兼容接口）
- 结构化JSON输出
- 多层JSON解析机制
- 自然语言备用解析

**技术规格:**
- API调用延迟: 5-10秒（取决于模型）
- 上下文长度: 4096+ tokens
- 输出格式: {"verdict": 0/1, "explanation": string}
- 容错机制: 多重解析策略

### 2.3 LLM检索增强智能体

**实现方式:**
- ChromaDB向量数据库 + LLM推理
- text-embedding-3-small嵌入模型
- 相似样本检索增强上下文
- 支持多数据集向量化存储

**工作流程:**
1. 输入文本 → 嵌入向量生成（1536维）
2. ChromaDB → 相似性检索（top-5）
3. 构建增强提示：检索样本 + 原文本
4. LLM推理 → 输出分类结果

**技术规格:**
- 嵌入维度: 1536
- 检索数量: 5个相似样本
- 向量数据库: ChromaDB持久化存储（./vector_db）
- 总处理时间: 9-11秒
- 支持数据集: HateSpeechStormfront, ImplicitHate等

**数据集支持:**
- HateSpeechOffensive
- HateSpeechStormfront
- ImplicitHate
- CHSD, COLDataset, ToxiCN等

## 3. 智能协调机制

### 3.1 权重初始化策略

**随机初始化方法:**
- 使用随机数生成器分配初始权重
- 权重范围: [0.2, 0.5]
- 归一化处理确保权重和为1.0
- 每次运行使用不同的随机种子

**实际初始化示例:**
```python
# 典型的初始权重分配
initial_weights = {
    'roberta': 0.444,      # RoBERTa智能体
    'llm_single': 0.235,   # LLM单智能体
    'llm_retrieval': 0.321 # LLM检索增强智能体
}
```

### 3.2 在线学习权重更新算法

**核心算法 - 对称奖励/惩罚机制:**
```python
def _adaptive_weight_update(self, agent_accuracies, overall_correct):
    """新的对称奖励/惩罚权重更新算法"""
    for agent_name in self.current_weights:
        if agent_name in agent_accuracies:
            # 计算奖励：预测正确为1，错误为0
            reward = agent_accuracies[agent_name]

            # 对称的权重变化：正确+0.5η，错误-0.5η
            change = self.adaptive_lr * (reward - 0.5)

            # 更新权重
            self.current_weights[agent_name] += change

            # 权重范围限制
            self.current_weights[agent_name] = max(0.05, min(0.8,
                self.current_weights[agent_name]))

    # 归一化权重
    self._normalize_weights()
```

**更新特点:**
- **对称机制**: 正确预测增加权重，错误预测减少权重
- **权重边界**: 限制在[0.05, 0.8]范围内确保系统稳定
- **实时学习**: 每个样本后立即更新权重
- **自适应学习率**: 默认0.01，可动态调整

### 3.3 动态权重预测

**文本特征分析:**
```python
def predict_weights(self, text, dataset_name=None):
    """基于文本特征和学习权重动态预测权重"""
    features = self.extract_text_features(text)
    base_weights = self.current_weights.copy()

    # 中文文本特征调整
    if features["has_chinese"] > 0.5:
        base_weights["llm_single"] += 0.025

    # 复杂文本特征调整
    if features["complexity"] > 0.7:
        base_weights["llm_retrieval"] += 0.03

    return self._normalize_weights(base_weights)
```

**特征提取指标:**
- 文本长度和复杂度
- 中文字符比例
- 特殊字符和符号
- 语言模式识别

## 4. 决策融合与并行处理

### 4.1 加权融合算法

**核心融合公式:**
```
weighted_prediction = Σ(weight_i × verdict_i) / Σ(weight_i)
final_verdict = 1 if weighted_prediction ≥ 0.5 else 0
```

**置信度计算:**
```python
# 综合置信度计算
consensus_score = 一致性程度  # 智能体预测一致性
weight_concentration = max(weights.values())  # 权重集中度
prediction_certainty = abs(weighted_prediction - 0.5) + 0.5

final_confidence = 0.4 * consensus_score + 0.3 * weight_concentration + 0.3 * prediction_certainty
```

**决策类型识别:**
- **unanimous**: 所有智能体预测一致
- **weak_majority**: 弱多数一致
- **no_consensus**: 无明显共识

### 4.2 真正的并行处理机制

**异步并行调用实现:**
```python
def _get_agent_predictions(self, text, dataset_name=None):
    """真正的并行调用 - 使用ThreadPoolExecutor"""
    import concurrent.futures

    def call_roberta_agent():
        return self.deberta_agent.detect(text, method="classification")

    def call_llm_single_agent():
        return self.llm_single_agent.detect(text)

    def call_llm_retrieval_agent():
        return self.llm_retrieval_agent.detect(text)

    # 并行执行
    with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
        future_roberta = executor.submit(call_roberta_agent)
        future_llm_single = executor.submit(call_llm_single_agent)
        future_llm_retrieval = executor.submit(call_llm_retrieval_agent)

        # 收集结果
        results = {
            "roberta": future_roberta.result(),
            "llm_single": future_llm_single.result(),
            "llm_retrieval": future_llm_retrieval.result()
        }

    return results
```

**性能优化特点:**
- **真正并行**: 三个智能体同时执行，不是顺序调用
- **处理时间**: 总时间约等于最慢智能体的时间
- **典型耗时**: RoBERTa(0.01s) + LLM单智能体(6s) + LLM检索(9s) = 总计约9-11秒

### 4.3 高级缓存系统

**多层缓存架构:**
```python
class HighPerformanceMultiAgentSystem:
    def __init__(self):
        # 多层LRU缓存
        self.feature_cache = LRUCache(max_size=1000)      # 特征缓存
        self.prediction_cache = LRUCache(max_size=2000)   # 预测缓存
        self.coordination_cache = LRUCache(max_size=1500) # 协调缓存
```

**缓存策略:**
- **特征缓存**: 文本特征提取结果
- **预测缓存**: 单个智能体预测结果
- **协调缓存**: 最终协调决策结果
- **线程安全**: 支持并发访问

## 5. 数据处理与向量数据库

### 5.1 数据集支持

**支持的数据集:**
- **HateSpeechOffensive**: 攻击性言论数据集
- **HateSpeechStormfront**: Stormfront论坛仇恨言论数据集
- **ImplicitHate**: 隐式仇恨言论数据集
- **CHSD**: 中文仇恨言论数据集
- **COLDataset**: 攻击性语言数据集
- **ToxiCN**: 中文毒性文本数据集
- **Dynamically-Generated-Hate-Speech-Dataset**: 动态生成仇恨言论数据集

**数据加载实现:**
```python
class DataLoader:
    def __init__(self, dataset_name):
        self.dataset_name = dataset_name
        self.dataset_path = f"./datasets/{dataset_name}"
        self.data = self._load_data()

    def _load_data(self):
        """根据数据集名称加载对应数据"""
        if self.dataset_name == "HateSpeechStormfront":
            return self._load_stormfront_data()
        elif self.dataset_name == "ImplicitHate":
            return self._load_implicit_hate_data()
        # ... 其他数据集加载逻辑

    def get_sample(self, index):
        return {
            "text": self.data[index]["text"],
            "label": self.data[index]["label"]
        }
```

### 5.2 ChromaDB向量数据库管理

**向量数据库架构:**
```python
class VectorDatabaseManager:
    def __init__(self):
        self.client = chromadb.PersistentClient(path="./vector_db")
        self.embedding_function = embedding_functions.OpenAIEmbeddingFunction(
            api_key=os.getenv("OPENAI_API_KEY"),
            model_name="text-embedding-3-small"
        )

    def create_collection(self, dataset_name):
        """为数据集创建向量集合"""
        collection = self.client.get_or_create_collection(
            name=dataset_name,
            embedding_function=self.embedding_function
        )
        return collection

    def add_training_data(self, dataset_name, texts, labels):
        """添加训练数据到向量数据库"""
        collection = self.get_collection(dataset_name)
        collection.add(
            documents=texts,
            metadatas=[{"label": int(label)} for label in labels],
            ids=[f"{dataset_name}_doc_{i}" for i in range(len(texts))]
        )
```

**向量检索机制:**
- **嵌入模型**: text-embedding-3-small (1536维)
- **检索数量**: Top-5相似样本
- **相似度计算**: 余弦相似度
- **存储路径**: ./vector_db/
- **持久化**: 支持数据持久化存储

## 6. 系统配置与使用

### 6.1 环境依赖

**核心依赖:**
```
# 数据科学与核心库
numpy==1.26.4
pandas==2.2.2
scikit-learn==1.5.1
matplotlib==3.9.2

# NLP与机器学习框架
torch==2.6.0+cu124
transformers==4.50.0
accelerate==0.26.1
sentencepiece==0.2.0

# API与Web服务
openai==1.88.0
fastapi==0.115.9
uvicorn==0.34.2
requests==2.31.0

# 向量数据库
chromadb==0.4.22

# 其他NLP工具
nltk
gensim
tqdm
```

### 6.2 高级系统配置

```python
@dataclass
class AdvancedSystemConfig:
    """智能协调系统配置"""
    system_type: str = "intelligent_coordination"
    enable_dynamic_weights: bool = True
    enable_performance_tracking: bool = True
    enable_online_learning: bool = True
    learning_rate: float = 0.01
    adaptation_threshold: float = 0.1
    confidence_threshold: float = 0.6
    weight_update_frequency: int = 10
    coordination_method: str = "dynamic_weighted"

    # 缓存配置
    enable_caching: bool = True
    cache_size: int = 1000

    # 并行处理配置
    max_workers: int = 3
    timeout_seconds: int = 30
```

### 6.3 系统使用接口

**基本使用:**
```python
# 创建智能协调系统
from offensive_speech_detection.models import create_advanced_system

system = create_advanced_system(
    system_type="intelligent_coordination",
    model="claude-3-5-sonnet-20240620",
    provider="api",
    dataset_name="HateSpeechStormfront"
)

# 单样本检测
result = system.detect("输入文本内容")

# 批量评估
python run_multi_agent_evaluation.py \
    --dataset HateSpeechStormfront \
    --num_samples 100 \
    --model claude-3-5-sonnet-20240620 \
    --enable-online-learning \
    --enable-dynamic-weights
```

**比较评估:**
```python
# 运行完整比较评估
python run_comparison_evaluation.py \
    --dataset HateSpeechStormfront \
    --num_samples 50 \
    --model claude-3-5-sonnet-20240620 \
    --provider api
```

## 7. 性能评估与监控

### 7.1 系统性能表现

**最新评估结果 (2025-07-30):**

| 数据集 | 准确率 | F1分数 | 精确率 | 召回率 |
|--------|--------|--------|--------|--------|
| HateSpeechOffensive | 87% | 92.22% | 92.77% | 91.67% |
| HateSpeechStormfront | 87% | 87.38% | 84.91% | 90% |
| ImplicitHate | 70% | 67.39% | 57.41% | 81.58% |
| **平均性能** | **81.33%** | **82.33%** | **78.36%** | **87.75%** |

### 7.2 详细日志格式

**多智能体协调日志:**
```json
{
  "metadata": {
    "run_id": "1dd7abdf",
    "dataset": "HateSpeechStormfront",
    "model": "claude-3-5-sonnet-20240620",
    "system_type": "intelligent_coordination",
    "system_config": {
      "enable_dynamic_weights": true,
      "enable_online_learning": true,
      "coordination_method": "dynamic_weighted",
      "learning_rate": 0.01
    }
  },
  "results": [
    {
      "sample_id": 1,
      "text": "输入文本内容",
      "true_label": 1,
      "final_verdict": 1,
      "final_confidence": 0.826,
      "agent_predictions": {
        "roberta": {"verdict": 1, "processing_time": 0.168},
        "llm_single": {"verdict": 1, "processing_time": 7.83},
        "llm_retrieval": {"verdict": 1, "processing_time": 11.036}
      },
      "consensus_type": "unanimous",
      "coordination_method": "dynamic_weighted",
      "weights": {
        "roberta": 0.419,
        "llm_single": 0.21,
        "llm_retrieval": 0.37
      },
      "processing_time": 11.04
    }
  ]
}
```

### 7.3 性能监控与分析

**关键性能指标:**
- **处理时间**: 平均9.79秒（并行处理）
- **缓存命中率**: 显著提升重复文本处理速度
- **权重学习效果**: 在线学习持续优化权重分配
- **共识类型分布**: unanimous, weak_majority, no_consensus

**模型兼容性分析:**
- **GPT系列**: 输出格式稳定，JSON解析成功率高
- **Claude系列**: 偶有格式不一致，但备用解析机制有效
- **本地模型**: 性能稳定，推理速度快
- **Gemini/DeepSeek**: 良好的API兼容性

### 7.4 系统优势总结

**技术创新点:**
1. **真正的并行处理**: 使用ThreadPoolExecutor实现智能体并行推理
2. **在线学习机制**: 对称奖励/惩罚算法持续优化权重
3. **多层缓存系统**: LRU缓存显著提升性能
4. **智能容错机制**: 多重JSON解析策略确保系统稳定性
5. **动态权重分配**: 基于文本特征和学习历史的智能权重预测

**应用价值:**
- **高准确性**: 多智能体协调显著提升检测准确率
- **强鲁棒性**: 单个智能体失败不影响整体系统
- **实时学习**: 系统在使用过程中持续改进
- **高效处理**: 并行处理和缓存机制提升响应速度
- **易扩展性**: 模块化设计便于添加新智能体

---

**文档更新时间**: 2025年7月30日
**框架版本**: v3.0 (智能协调系统)
**技术栈**: Python 3.8+, PyTorch 2.6.0, Transformers 4.50.0, ChromaDB 0.4.22, OpenAI API 1.88.0
**项目状态**: 生产就绪，支持多种LLM模型和本地部署
